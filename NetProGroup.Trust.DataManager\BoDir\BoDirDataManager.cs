// <copyright file="BoDirDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using AutoMapper.QueryableExtensions;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines.BODirector;
using NetProGroup.Trust.DomainShared.Enums;
using System.Linq.Expressions;
using System.Reflection;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.BoDir
{
    /// <summary>
    /// Manager class for handling Beneficial Owners and Directors data operations.
    /// </summary>
    public class BoDirDataManager : IBoDirDataManager
    {
        private const string PendingUpdateRequest = "PENDING UPDATE REQUEST";
        private const string MissingInformation = "MISSING INFORMATION";
        private const string MissingBODir = "MISSING BO/DIR";
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;
        private MapperConfiguration _mapperConfiguration;

        /// <summary>
        /// Initializes a new instance of the <see cref="BoDirDataManager"/> class.
        /// </summary>
        /// <param name="legalEntitiesRepository">The repository for legal entities.</param>
        /// <param name="authorizationFilterExpressionFactory">The factory for authorization filter expressions.</param>
        public BoDirDataManager(
            ILegalEntitiesRepository legalEntitiesRepository, IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory)
        {
            _legalEntitiesRepository = legalEntitiesRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
        }

        /// <inheritdoc />
        public async Task<IPagedList<BoDirItemDTO>> SearchBoDirsAsync(SearchBoDirRequest request)
        {
            // Build base query with conditional includes
            var baseQuery = BuildBaseQuery(request);

            // Apply filters and sorting
            var filteredQuery = ApplyFiltersAndSort(baseQuery, request);

            // Paginate the results
            var result = await PaginateQueryAsync(filteredQuery, request);

            foreach (var item in result.Where(r => string.IsNullOrEmpty(r.Specifics)))
            {
                switch (item.Position)
                {
                    case LegalEntityPositions.Director:
                        {
                            item.Specifics = GetHasDirMissingInformation(item) ? MissingInformation : null;
                            break;
                        }

                    case LegalEntityPositions.BeneficialOwner:
                        {
                            item.Specifics = GetHasBOMissingInformation(item) ? MissingInformation : null;
                            break;
                        }
                }
            }

            return result;
        }

        /// <summary>
        /// Builds the base query with necessary includes and optimizations.
        /// </summary>
        private IQueryable<LegalEntity> BuildBaseQuery(SearchBoDirRequest request)
        {
            return _legalEntitiesRepository
                   .GetQueryable()
                   //.IncludeIf(IsBeneficialOwnerIncluded(request),
                   //    le => le.BeneficialOwners,
                   //    bo => bo.BeneficialOwnerHistories)
                   //.IncludeIf(IsDirectorIncluded(request),
                   //    le => le.Directors,
                   //    dir => dir.DirectorHistories)
                   //.Include(le => le.MasterClient)
                   .Where(le => le.IsActive) // Only include active entities
                   .Where(_authorizationFilterExpressionFactory.GetLegalEntityJurisdictionFilterPredicate(request))
                   .AsNoTracking()
                   .AsSplitQuery();
        }

        /// <summary>
        /// Applies filters and sorting based on the request.
        /// </summary>
        private IQueryable<BoDirItemDTO> ApplyFiltersAndSort(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            //// Combine filters for multiple specifics
            //if (request.Specifics != null && request.Specifics.Count != 0)
            //{
            //    baseQuery = ApplySpecificsFilters(baseQuery, request);
            //}

            // Transform the base query into DTOs based on request criteria
            var query = TransformToDTO(baseQuery, request);

            query = BoDirItemDtosFilters(request, query);

            // Apply sorting
            return ApplySorting(query, request);
        }

        private static IQueryable<LegalEntity> ApplySpecificsFilters(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // Combine filters based on the selected specifics
            if (request.Specifics.Contains(BoDirSpecifics.NoBoDirInformation))
            {
                baseQuery = baseQuery.Where(legalEntity =>
                    legalEntity.BeneficialOwners.Count == 0 && legalEntity.Directors.Count == 0);
            }

            return baseQuery;
        }

        private static IQueryable<BoDirItemDTO> BoDirItemDtosFilters(SearchBoDirRequest request, IQueryable<BoDirItemDTO> query)
        {
            var hasMissing = request.Specifics?.Contains(BoDirSpecifics.MissingInformation) == true;
            var hasComplete = request.Specifics?.Contains(BoDirSpecifics.BoDirInformation) == true;

            query = query.Where(dto =>
                (hasMissing && dto.HasMissingInformation) ||
                (hasComplete && !dto.HasMissingInformation));

            if (request.ConfirmedDateFrom.HasValue && request.ConfirmedDateTo.HasValue)
            {
                query = query.Where(dto =>
                    dto.ConfirmedDate.HasValue && dto.ConfirmedDate.Value >= request.ConfirmedDateFrom.Value
                                               && dto.ConfirmedDate.Value <= request.ConfirmedDateTo.Value);
            }
            else if (request.ConfirmedDateFrom.HasValue)
            {
                query = query.Where(dto =>
                    dto.ConfirmedDate.HasValue && dto.ConfirmedDate.Value >= request.ConfirmedDateFrom.Value);
            }
            else if (request.ConfirmedDateTo.HasValue)
            {
                query = query.Where(dto =>
                    dto.ConfirmedDate.HasValue && dto.ConfirmedDate.Value <= request.ConfirmedDateTo.Value);
            }

            if (request.DataStatuses.Count > 0)
            {
                query = query.Where(dto =>
                    request.DataStatuses.Contains(dto.Status));
            }

            if (request.ProductionOffice != null)
            {
                query = query.Where(dto =>
                    dto.ProductionOffice.Contains(request.ProductionOffice));
            }

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                query = query.Where(dto =>
                    dto.LegalEntityName.Contains(request.SearchTerm) ||
                    dto.DirectorName.Contains(request.SearchTerm) ||
                    dto.MasterClientCode.Contains(request.SearchTerm) ||
                    dto.VPEntityNumber.Contains(request.SearchTerm) ||
                    dto.EntityPortalCode.Contains(request.SearchTerm) ||
                    dto.ReferralOffice.Contains(request.SearchTerm) ||
                    dto.ProductionOffice.Contains(request.SearchTerm));
            }

            return query;
        }

        /// <summary>
        /// Transforms the base query of legal entities into a filtered and projected DTO query based on the search request.
        /// </summary>
        /// <param name="baseQuery">The base query of legal entities.</param>
        /// <param name="request">The search request containing filtering criteria.</param>
        /// <returns>An IQueryable of BoDirItemDTO matching the search criteria.</returns>
        private IQueryable<BoDirItemDTO> TransformToDTO(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // If "NoBoDirInformation" is specified, return all LegalEntities as BoDirItemDTO
            if (request.Specifics?.Contains(BoDirSpecifics.NoBoDirInformation) == true)
            {
                baseQuery = baseQuery.Where(legalEntity =>
                    legalEntity.BeneficialOwners.Count == 0 && legalEntity.Directors.Count == 0);
                return baseQuery.ProjectTo<BoDirItemDTO>(_mapperConfiguration);
            }

            IQueryable<BoDirItemDTO> query;
            switch (request.Position)
            {
                case BoDirPosition.Director:
                    query = baseQuery.SelectMany(le => le.Directors).ProjectTo<BoDirItemDTO>(_mapperConfiguration);
                    break;
                case BoDirPosition.BeneficialOwner:
                    query = baseQuery.SelectMany(le => le.BeneficialOwners).ProjectTo<BoDirItemDTO>(_mapperConfiguration);
                    break;
                default:
                    query = baseQuery.SelectMany(le => le.BeneficialOwners.AsQueryable().ProjectTo<BoDirItemDTO>(_mapperConfiguration))
                                     .Concat(baseQuery.SelectMany(le => le.Directors.AsQueryable().ProjectTo<BoDirItemDTO>(_mapperConfiguration)));
                    break;
            }

            return query;


            //if (IsDirector(request) && !IsBeneficialOwner(request))
            //{

            //}

            //// Handle specific filtering for Beneficial Owners or combine Directors and Beneficial Owners
            //return IsBeneficialOwner(request) ? HandleBeneficialOwners(baseQuery, request) :

            //    // Handle both Directors and Beneficial Owners if no specific position is specified
            //    HandleCombinedResults(baseQuery, request);
        }

        /// <summary>
        /// Processes the query for Directors based on the search request.
        /// </summary>
        /// <param name="baseQuery">The base query of legal entities.</param>
        /// <param name="request">The search request containing filtering criteria.</param>
        /// <returns>An IQueryable of BoDirItemDTO for directors.</returns>
        private static IQueryable<BoDirItemDTO> HandleDirectors(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // Query for directors with no missing information
            var directors = baseQuery
                .SelectMany(le => le.Directors)
                .Select(DirectorToDTO);

            // Query for directors with missing information
            var directorsMissing = baseQuery
                .SelectMany(le => le.Directors.AsQueryable().Where(GetHasDirMissingInformationPredicate(request)))
                .Select(DirectorToDTO);

            // Combine the results based on the specifics in the request
            return CombineQueries(directors, directorsMissing, request);
        }

        /// <summary>
        /// Processes the query for Beneficial Owners based on the search request.
        /// </summary>
        /// <param name="baseQuery">The base query of legal entities.</param>
        /// <param name="request">The search request containing filtering criteria.</param>
        /// <returns>An IQueryable of BoDirItemDTO for beneficial owners.</returns>
        private static IQueryable<BoDirItemDTO> HandleBeneficialOwners(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // Query for beneficial owners with no missing information
            var beneficialOwners = baseQuery
                .SelectMany(le => le.BeneficialOwners)
                .Select(BeneficialOwnerToDTO);

            // Query for beneficial owners with missing information
            var beneficialOwnersMissing = baseQuery
                .SelectMany(le => le.BeneficialOwners.AsQueryable().Where(GetHasBOMissingInformationPredicate(request)))
                .Select(BeneficialOwnerToDTO);

            // Combine the results based on the specifics in the request
            return CombineQueries(beneficialOwners, beneficialOwnersMissing, request);
        }

        /// <summary>
        /// Processes the query for both Directors and Beneficial Owners when no specific position is specified.
        /// </summary>
        /// <param name="baseQuery">The base query of legal entities.</param>
        /// <param name="request">The search request containing filtering criteria.</param>
        /// <returns>An IQueryable of BoDirItemDTO combining directors and beneficial owners.</returns>
        private static IQueryable<BoDirItemDTO> HandleCombinedResults(IQueryable<LegalEntity> baseQuery, SearchBoDirRequest request)
        {
            // Query for directors and beneficial owners with no missing information
            var directors = baseQuery.SelectMany(le => le.Directors).Select(DirectorToDTO);
            var beneficialOwners = baseQuery.SelectMany(le => le.BeneficialOwners).Select(BeneficialOwnerToDTO);

            // Query for directors and beneficial owners with missing information
            var directorsMissing = baseQuery
                .SelectMany(le => le.Directors.AsQueryable().Where(GetHasDirMissingInformationPredicate(request)))
                .Select(DirectorToDTO);

            var beneficialOwnersMissing = baseQuery
                .SelectMany(le => le.BeneficialOwners.AsQueryable().Where(GetHasBOMissingInformationPredicate(request)))
                .Select(BeneficialOwnerToDTO);

            // Combine all results
            return directors
                .Concat(beneficialOwners)
                .Concat(directorsMissing)
                .Concat(beneficialOwnersMissing);
        }

        /// <summary>
        /// Combines queries for complete and missing information based on request specifics.
        /// </summary>
        /// <param name="completeItems">The query for items with no missing information.</param>
        /// <param name="missingItems">The query for items with missing information.</param>
        /// <param name="request">The search request containing specifics.</param>
        /// <returns>An IQueryable combining the complete and/or missing items based on the request.</returns>
        private static IQueryable<BoDirItemDTO> CombineQueries(
            IQueryable<BoDirItemDTO> completeItems,
            IQueryable<BoDirItemDTO> missingItems,
            SearchBoDirRequest request)
        {
            var hasMissing = request.Specifics?.Contains(BoDirSpecifics.MissingInformation) == true;
            var hasComplete = request.Specifics?.Contains(BoDirSpecifics.BoDirInformation) == true;

            // Combine complete and missing items based on request specifics
            if (hasMissing && hasComplete)
            {
                return missingItems.Concat(completeItems);
            }

            return hasMissing ? missingItems : completeItems;
        }

        /// <summary>
        /// Gets the expression to select BO's with missing information base on the OfficerType.
        /// </summary>
        /// <param name="request">The request with search parameters.</param>
        /// <returns>The expression.</returns>
        private static Expression<Func<BeneficialOwner, bool>> GetHasBOMissingInformationPredicate(SearchBoDirRequest request)
        {
            return beneficialOwner => request.Specifics.Contains(BoDirSpecifics.MissingInformation) &&
                (

                    // Nevis
                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP01 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         !beneficialOwner.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfBirth) ||
                         string.IsNullOrEmpty(beneficialOwner.Nationality) ||
                         string.IsNullOrEmpty(beneficialOwner.ResidentialAddress))) ||

                    ((beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP02 ||
                      beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP03 ||
                      beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP04 ||
                      beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP05 ||
                      beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.KNTP06) &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         string.IsNullOrEmpty(beneficialOwner.Country) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address))) ||

                    // BVI
                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP01 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         !beneficialOwner.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.PlaceOfBirth) ||
                         string.IsNullOrEmpty(beneficialOwner.Nationality) ||
                         string.IsNullOrEmpty(beneficialOwner.ResidentialAddress))) ||

                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP02 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country))) ||

                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP03 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country) ||
                         string.IsNullOrEmpty(beneficialOwner.NameOfRegulator) ||
                         string.IsNullOrEmpty(beneficialOwner.JurisdictionOfRegulator))) ||

                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP04 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country) ||
                         string.IsNullOrEmpty(beneficialOwner.SovereignState))) ||

                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP05 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country) ||
                         string.IsNullOrEmpty(beneficialOwner.StockExchangeCode) ||
                         string.IsNullOrEmpty(beneficialOwner.StockExchangeName))) ||

                    (beneficialOwner.OfficerTypeCode == BODirectorOfficerTypeCode.VGTP06 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country))));
        }

        private static bool GetHasBOMissingInformation(BoDirItemDTO beneficialOwner)
        {
            return

                    // Nevis
                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP01 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         !beneficialOwner.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfBirth) ||
                         string.IsNullOrEmpty(beneficialOwner.Nationality) ||
                         string.IsNullOrEmpty(beneficialOwner.ResidentialAddress))) ||

                    ((beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP02 ||
                      beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP03 ||
                      beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP04 ||
                      beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP05 ||
                      beneficialOwner.OfficerType == BODirectorOfficerTypeCode.KNTP06) &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfFormation) ||
                         string.IsNullOrEmpty(beneficialOwner.Address))) ||

                    // BVI
                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP01 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         !beneficialOwner.DateOfBirth.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.PlaceOfBirth) ||
                         string.IsNullOrEmpty(beneficialOwner.Nationality) ||
                         string.IsNullOrEmpty(beneficialOwner.ResidentialAddress))) ||

                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP02 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.Country))) ||

                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP03 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfFormation) ||
                         string.IsNullOrEmpty(beneficialOwner.NameOfRegulator) ||
                         string.IsNullOrEmpty(beneficialOwner.JurisdictionOfRegulator))) ||

                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP04 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfFormation) ||
                         string.IsNullOrEmpty(beneficialOwner.SovereignState))) ||

                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP05 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfFormation) ||
                         string.IsNullOrEmpty(beneficialOwner.StockExchangeCode) ||
                         string.IsNullOrEmpty(beneficialOwner.StockExchangeName))) ||

                    (beneficialOwner.OfficerType == BODirectorOfficerTypeCode.VGTP06 &&
                        (string.IsNullOrEmpty(beneficialOwner.Name) ||
                         string.IsNullOrEmpty(beneficialOwner.IncorporationNr) ||
                         !beneficialOwner.IncorporationDate.HasValue ||
                         string.IsNullOrEmpty(beneficialOwner.Address) ||
                         string.IsNullOrEmpty(beneficialOwner.CountryOfFormation)));
        }

        /// <summary>
        /// Gets the expression to select Directors with missing information base on IsIndividual.
        /// </summary>
        /// <param name="request">The request with search parameters.</param>
        /// <returns>The expression.</returns>
        private static Expression<Func<Director, bool>> GetHasDirMissingInformationPredicate(SearchBoDirRequest request)
        {
            return director =>
                request.Specifics.Contains(BoDirSpecifics.MissingInformation) &&
                (
                    director.IsIndividual
                        ? string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.ResidentialAddress) ||
                          !director.DateOfBirth.HasValue ||
                          string.IsNullOrEmpty(director.CountryOfBirth) ||
                          string.IsNullOrEmpty(director.Nationality)

                        // Non-individual directors (missing information)
                        : string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          string.IsNullOrEmpty(director.IncorporationNr) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.Address) ||
                          !director.IncorporationDate.HasValue ||
                          string.IsNullOrEmpty(director.Country));
        }

        private static bool GetHasDirMissingInformation(BoDirItemDTO director)
        {
            return
                    director.IsIndividual
                        ? string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.ResidentialAddress) ||
                          !director.DateOfBirth.HasValue ||
                          string.IsNullOrEmpty(director.CountryOfBirth) ||
                          string.IsNullOrEmpty(director.Nationality)

                        // Non-individual directors (missing information)
                        : string.IsNullOrEmpty(director.RelationType) ||
                          string.IsNullOrEmpty(director.Name) ||
                          string.IsNullOrEmpty(director.IncorporationNr) ||
                          !director.AppointmentDate.HasValue ||
                          string.IsNullOrEmpty(director.Address) ||
                          !director.IncorporationDate.HasValue ||
                          string.IsNullOrEmpty(director.CountryOfFormation);
        }

        /// <summary>
        /// Applies sorting based on the request parameters.
        /// </summary>
        private static IQueryable<BoDirItemDTO> ApplySorting(IQueryable<BoDirItemDTO> query, SearchBoDirRequest request)
        {
            var overrides = new Dictionary<string, Expression<Func<BoDirItemDTO, object>>>()
            {
                {
                    nameof(BoDirItemDTO.Status), s => s.Status == LegalEntityRelationStatus.Initial ? 1 :
                        s.Status == LegalEntityRelationStatus.Refreshed ? 2 :
                        s.Status == LegalEntityRelationStatus.Confirmed ? 3 :
                        s.Status == LegalEntityRelationStatus.PendingUpdateRequest ? 4 :
                        s.Status == LegalEntityRelationStatus.UpdateReceived ? 5 :
                        - 1
                }
            };

            Expression<Func<BoDirItemDTO, object>> defaultSort = s => s.LegalEntityName;

            return query.SortBySpecification<BoDirItemDTO, SearchBoDirRequestDTO>(request.ToSortingInfo(), overrides, defaultSort);
        }

        /// <summary>
        /// Paginates the query and returns a paged result.
        /// </summary>
        private static async Task<IPagedList<BoDirItemDTO>> PaginateQueryAsync(IQueryable<BoDirItemDTO> query, SearchBoDirRequest request)
        {

            var totalCount = await query.CountAsync();
            var items = await query.ToPagedListAsync(request.PageNumber, request.PageSize);
            return items;
            //return new StaticPagedList<BoDirItemDTO>(items, request.PageNumber, request.PageSize, totalCount);
        }

        /// <summary>
        /// Expression to map a Director to a BoDirItemDTO.
        /// </summary>
        private static readonly Expression<Func<Director, BoDirItemDTO>> DirectorToDTO = d => new BoDirItemDTO
        {
            Id = d.Id,
            DirectorName = d.Name,
            Position = "Director",
            LegalEntityName = d.LegalEntity.Name,
            VPEntityNumber = d.LegalEntity.Code,
            EntityPortalCode = d.LegalEntity.LegacyCode,
            MasterClientCode = d.LegalEntity.MasterClientCode,
            ReferralOffice = d.LegalEntity.ReferralOffice,
            ProductionOffice = d.LegalEntity.ProductionOffice,
            ConfirmedDate = d.DirectorHistories.OrderBy(o => o.CreatedAt).Where(o => o.ConfirmedAt != null).Select(o => o.ConfirmedAt).LastOrDefault(),
            Status = d.DirectorHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status).LastOrDefault(),
            RequestUpdateDate = d.DirectorHistories.OrderBy(o => o.CreatedAt).Where(o => o.UpdateRequestedAt != null).Select(o => o.UpdateRequestedAt).LastOrDefault(),
            DirectorType = d.RelationType,
            OfficerType = d.OfficerTypeName,
            DirectorVPCode = d.Code,
            Specifics = d.DirectorHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status == LegalEntityRelationStatus.PendingUpdateRequest ? PendingUpdateRequest : string.Empty).LastOrDefault(),
            IsIndividual = d.IsIndividual,

            // These ar not externally displayed (JsonIgnore) but only used to check if data is missing
            RelationType = d.RelationType,
            Name = d.Name,
            AppointmentDate = d.AppointmentDate,
            ServiceAddress = d.ServiceAddress,
            ResidentialAddress = d.ResidentialAddress,
            DateOfBirth = d.DateOfBirth,
            CountryOfBirth = d.CountryOfBirth,
            Nationality = d.Nationality,
            IncorporationNr = d.IncorporationNr,
            IncorporationDate = d.IncorporationDate,
            IncorporationPlace = d.IncorporationPlace,
            Address = d.Address,

            PlaceOfBirth = d.PlaceOfBirth,
            CountryOfFormation = d.Country,
            NameOfRegulator = "",
            JurisdictionOfRegulator = "",
            SovereignState = "",
            StockExchangeCode = "",
            StockExchangeName = "",
            Country = d.Country,
            TIN = d.TIN
        };

        /// <summary>
        /// Expression to map a Beneficial Owner to a BoDirItemDTO.
        /// </summary>
        private static readonly Expression<Func<BeneficialOwner, BoDirItemDTO>> BeneficialOwnerToDTO = bo => new BoDirItemDTO
        {
            Id = bo.Id,
            DirectorName = bo.Name,
            Position = "BeneficialOwner",
            LegalEntityName = bo.LegalEntity.Name,
            VPEntityNumber = bo.LegalEntity.Code,
            EntityPortalCode = bo.LegalEntity.LegacyCode,
            MasterClientCode = bo.LegalEntity.MasterClientCode,
            ReferralOffice = bo.LegalEntity.ReferralOffice,
            ProductionOffice = bo.OfficerTypeCode,
            ConfirmedDate = bo.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Where(o => o.ConfirmedAt != null).Select(o => o.CreatedAt).LastOrDefault(),
            Status = bo.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status).LastOrDefault(),
            RequestUpdateDate = bo.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Where(o => o.UpdateRequestedAt != null).Select(o => o.UpdateRequestedAt).LastOrDefault(),
            DirectorType = bo.OfficerTypeCode,
            OfficerType = bo.OfficerTypeCode,
            DirectorVPCode = bo.Code,
            Specifics = bo.BeneficialOwnerHistories.OrderBy(o => o.CreatedAt).Select(o => o.Status == LegalEntityRelationStatus.PendingUpdateRequest ? PendingUpdateRequest : string.Empty).LastOrDefault(),
            IsIndividual = bo.IsIndividual,

            // These ar not externally displayed (JsonIgnore) but only used to check if data is missing
            Name = bo.Name,
            DateOfBirth = bo.DateOfBirth,
            PlaceOfBirth = bo.PlaceOfBirth,
            CountryOfBirth = bo.CountryOfBirth,
            Nationality = bo.Nationality,
            ResidentialAddress = bo.ResidentialAddress,
            IncorporationNr = bo.IncorporationNr,
            IncorporationDate = bo.IncorporationDate,
            IncorporationPlace = "",
            CountryOfFormation = bo.CountryOfFormation,
            NameOfRegulator = bo.NameOfRegulator,
            JurisdictionOfRegulator = bo.JurisdictionOfRegulator,
            SovereignState = bo.SovereignState,
            StockExchangeCode = bo.StockExchangeCode,
            StockExchangeName = bo.StockExchangeName,
            RelationType = "",
            AppointmentDate = bo.AppointmentDate,
            ServiceAddress = bo.ServiceAddress,
            Address = bo.Address,
            Country = bo.Country,
            TIN = bo.TIN
        };

        /// <summary>
        /// Gets the expression to use when asked for legal entities without Bo/Dir.
        /// </summary>
        private static readonly Expression<Func<LegalEntity, BoDirItemDTO>> LegalEntityToDTO = legalEntity => new BoDirItemDTO
        {
            Id = legalEntity.Id,
            ProductionOffice = legalEntity.ProductionOffice,
            ReferralOffice = legalEntity.ReferralOffice,
            LegalEntityName = legalEntity.Name,
            VPEntityNumber = legalEntity.Code,
            EntityPortalCode = legalEntity.LegacyCode,
            MasterClientCode = legalEntity.MasterClientCode,
            Specifics = MissingBODir,
        };

        #region Helper Methods

        private static bool IsDirectorIncluded(SearchBoDirRequest request) =>
            request.Position is null || IsDirector(request);

        private static bool IsBeneficialOwnerIncluded(SearchBoDirRequest request) =>
            request.Position is null || IsBeneficialOwner(request);

        private static bool IsDirector(SearchBoDirRequest request) =>
            request?.Position == BoDirPosition.Director;

        private static bool IsBeneficialOwner(SearchBoDirRequest request) =>
            request?.Position == BoDirPosition.BeneficialOwner;

        #endregion
    }
}
