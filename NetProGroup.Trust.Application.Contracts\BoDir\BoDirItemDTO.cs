// <copyright file="BoDirItemDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.DomainShared.Enums;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Application.Contracts.BoDir
{
    /// <summary>
    /// DTO representing a Beneficial Owner or Director entry.
    /// </summary>
    public class BoDirItemDTO
    {
        /// <summary>
        /// Gets or sets the Id.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the Production Office.
        /// </summary>
        public string ProductionOffice { get; set; }

        /// <summary>
        /// Gets or sets the Referral Office.
        /// </summary>
        public string ReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the Entity Name.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the VP Entity Number.
        /// </summary>
        public string VPEntityNumber { get; set; }

        /// <summary>
        /// Gets or sets the Entity Portal Code.
        /// </summary>
        public string EntityPortalCode { get; set; }

        /// <summary>
        /// Gets or sets the Master Client Code.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the Director/Member/BO VP Code.
        /// </summary>
        public string DirectorVPCode { get; set; }

        /// <summary>
        /// Gets or sets the Director/Member/BO Name.
        /// </summary>
        public string DirectorName { get; set; }

        /// <summary>
        /// Gets or sets the Position.
        /// </summary>
        public string Position { get; set; }

        /// <summary>
        /// Gets or sets the Type of Director/BO.
        /// </summary>
        public string DirectorType { get; set; }

        /// <summary>
        /// Gets or sets the OfficerType.
        /// This contains the code for BO's and the name for Directors.
        /// </summary>
        public string OfficerType { get; set; }

        /// <summary>
        /// Gets or sets the Specifics.
        /// </summary>
        public string Specifics { get; set; }

        public bool HasMissingInformation { get; set; }

        /// <summary>
        /// Gets or sets the Status.
        /// </summary>
        public LegalEntityRelationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the Request Update Date.
        /// </summary>
        public DateTime? RequestUpdateDate { get; set; }

        /// <summary>
        /// Gets or sets the Confirmed Date.
        /// </summary>
        public DateTime? ConfirmedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the relation is an individual.
        /// </summary>
        public bool IsIndividual { get; set; }

        // The following fields are not used for output to the frontend but to collect the BO/Dir information so it can be checked on missing information.
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        [JsonIgnore]
        public string Name { get; set; }

        [JsonIgnore]
        public DateTime? DateOfBirth { get; set; }

        [JsonIgnore]
        public string CountryOfBirth { get; set; }

        [JsonIgnore]
        public string PlaceOfBirth { get; set; }

        [JsonIgnore]
        public DateTime? IncorporationDate { get; set; }

        [JsonIgnore]
        public string IncorporationPlace { get; set; }

        [JsonIgnore]
        public DateTime? AppointmentDate { get; set; }

        [JsonIgnore]
        public string IncorporationNr { get; set; }

        [JsonIgnore]
        public string Address { get; set; }

        [JsonIgnore]
        public string ServiceAddress { get; set; }

        [JsonIgnore]
        public string TIN { get; set; }

        [JsonIgnore]
        public string Nationality { get; set; }

        [JsonIgnore]
        public string ResidentialAddress { get; set; }

        [JsonIgnore]
        public string Country { get; set; }

        [JsonIgnore]
        public string CountryOfFormation { get; set; }

        [JsonIgnore]
        public string StockExchangeCode { get; set; }

        [JsonIgnore]
        public string StockExchangeName { get; set; }

        [JsonIgnore]
        public string SovereignState { get; set; }

        [JsonIgnore]
        public string NameOfRegulator { get; set; }

        [JsonIgnore]
        public string JurisdictionOfRegulator { get; set; }

        [JsonIgnore]
        public string RelationType { get; set; }

#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}
